"""
Jetveo API Client - External API integration for product data.
Handles communication with Jetveo API for product information, PIN validation, and heartbeat.

Features:
1. Product Data: Fetch product information by EAN, search products, get categories
2. PIN Validation: Validate operator PINs against Jetveo server
3. Heartbeat Service: Automatic "I'm alive" messages sent periodically

PIN Check Integration:
- The check_pin() method is ready to use for PIN validation
- To switch from MockResponse to real API in box/router.py:
  1. Uncomment: response_data = await jetveo_client.check_pin(pin, SERIAL_NUMBER or "unknown")
  2. Remove the MockResponse lines
  3. Ensure EXTERNAL_API_BASE_URL and EXTERNAL_API_TOKEN are properly configured

Heartbeat Service:
- Automatically started when the application starts (see main.py lifespan)
- Only runs if EXTERNAL_API_ENABLE environment variable is set to true
- Sends POST /api/imalive every 9 minutes by default
- Configurable via JETVEO_HEARTBEAT_INTERVAL_MINUTES environment variable
- Uses SERIAL_NUMBER from environment for device identification
"""

import logging
import aiohttp
import asyncio
import mysql.connector
import json
import os
from typing import Optional, Dict, Any, Tuple, List
from os import getenv
from dotenv import load_dotenv
import threading
from datetime import datetime, timedelta
import time

from sale.models import ExternalProduct, ExternalProductResponse

load_dotenv()

logger = logging.getLogger(__name__)

class JetveoClient:
    """Client for Jetveo external API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = getenv("EXTERNAL_API_BASE_URL", "https://4a8fa4e1-8948-4b4c-9baa-4839d158ad96.eu.jetveo.io")
        self.partner_code = getenv("EXTERNAL_API_PARTNER_CODE", "drmax")
        self.api_token = getenv("EXTERNAL_API_TOKEN", "Gj1lojJdMIrgC13psFsWwveas8PYLdUC")
        self.timeout = int(getenv("EXTERNAL_API_TIMEOUT", "10"))
        self.heartbeat_interval = int(getenv("JETVEO_HEARTBEAT_INTERVAL_MINUTES", "9")) * 60  # Convert to seconds
        self.serial_number = getenv("SERIAL_NUMBER", "unknown")
        self.external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
        self.request_queue_duration = int(getenv("EXTERNAL_API_REQUEST_QUEUE_DURATION", "300"))  # 5 minutes default
        self.temperature_sensor_pins = getenv("TEMPERATURE_SENSOR_PINS", "1,2,3,4,5,6,7,8,9").split(",")
        self.comet_ip_addresses = getenv("COMET_IP_ADDRESSES", "*************,*************").split(",")
        self._heartbeat_task = None
        self.echo_loop_running = False

    def _get_db_connection(self):
        """Get database connection for server request queue"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def fetch_product_by_ean(self, ean: str) -> Optional[ExternalProduct]:
        """
        Fetch product information from external API by EAN code.
        Only fetches if external API is enabled.

        Args:
            ean: EAN code to search for

        Returns:
            ExternalProduct object if found, None otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping product fetch")
            return None

        payload = {
            "ean": ean
        }

        success, data = await self.send_to_server("/api/product", payload, "product_search", method="get")
        return data


    
    async def search_products(self, query: str, limit: int = 100) -> Optional[ExternalProductResponse]:
        """
        Search products in external API.
        Only searches if external API is enabled.

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            ExternalProductResponse object or None if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping product search")
            return None

        payload = {
            "offset": 0,
            "limit": limit,
            "partner-code": self.partner_code,
            "q": query
        }

        success, data = await self.send_to_server("/api/product", payload, "product_search", method="get")
        return data
    
    
    async def get_product_categories(self) -> Optional[Dict[str, Any]]:
        """
        Get product categories from external API.
        Only fetches if external API is enabled.

        Returns:
            Categories data or None if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping categories fetch")
            return None

        payload = {
            "partner-code": self.partner_code
        }

        success, data = await self.send_to_server("/api/categories", payload, "categories", method="get")
        return data


    async def check_pin(self, pin: str, serial_number: str) -> Optional[Dict[str, Any]]:
        """
        Check PIN validity with external API.
        Only checks if external API is enabled.

        Args:
            pin: PIN code to validate
            serial_number: Device serial number

        Returns:
            Dict with PIN check result or None if failed/disabled
            Expected response format:
            {
                "serial_number": "xxxx",
                "status": "allow" | "deny",
                "operator_id": int,
                "name": "Operator Name",
                "type": "1" | "2" | etc.
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping PIN check")
            return None

        payload = {
            "serial_number": serial_number,
            "scanned_pin": pin
        }

        success, data = await self.send_to_server("/api/pin_check", payload, "pin_check")
        return data
    

    async def send_heartbeat(self) -> bool:
        """
        Send "I'm alive" heartbeat to Jetveo server.
        Only sends if external API is enabled.

        Returns:
            True if heartbeat sent successfully, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping heartbeat")
            return False

        payload = {
            "SerialNumber": self.serial_number
        }

        success, data = await self.send_to_server("/api/imalive", payload, "heartbeat")
        return success

    async def echo_loop(self):
        """
        Internal echo loop that runs periodically.
        Also retries failed requests from the queue.
        """
        self.logger.info(f"Starting echo loop with interval {self.heartbeat_interval} seconds")

        while self.echo_loop_running:

            try:
                # await asyncio.sleep(self.heartbeat_interval * 60)  # Convert minutes to seconds
                await asyncio.sleep(2)      #DELETE AFTER TESTING

                # send hearbeat
                await self.send_heartbeat()

                # retry failed requests
                await self.retry_failed_requests()

                # send electronic sensors data
                from hardware.temperatures import temperature_controller
                electronic_temps = await temperature_controller.get_electronic_sensor_temp_all()
                await self.send_temperature_electronic(electronic_temps)

                # send comet sensores data
                # comet_temps = await temperature_controller.get_comet_sensors_temp_all()
                # await self.send_temperature_comet(comet_temps)

            except ImportError as e:
                self.logger.warning(f"Could not import temperature controller: {e}")
            except Exception as e:
                self.logger.error(f"Error reading temperature sensors: {e}")

            except asyncio.CancelledError:
                self.logger.info("Echo loop cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in echo loop: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(60)



    def start_echo_loop(self):
        """
        Start the periodic heartbeat in the background.
        Only starts if external API is enabled.
        """
        if not self.external_api_enabled:     #TODO: uncomment after
            self.logger.info("External API is disabled, echo loop will not start")
            return

        if self.echo_loop_running:
            self.logger.warning("Echo loop is already running")
            return

        self.echo_loop_running = True

        # Create new event loop for the echo loop if we're not in an async context
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, create task
                self._heartbeat_task = loop.create_task(self.echo_loop())
            else:
                # Start in a separate thread
                def run_echo_loop():
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.echo_loop())
                    except Exception as e:
                        self.logger.error(f"Echo loop error: {e}")
                    finally:
                        self.echo_loop_running = False

                heartbeat_thread = threading.Thread(target=run_echo_loop, daemon=True)
                heartbeat_thread.start()

        except RuntimeError:
            # No event loop, start in a separate thread
            def run_echo_loop():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.echo_loop())
                except Exception as e:
                    self.logger.error(f"Echo loop error: {e}")
                finally:
                    self.echo_loop_running = False

            heartbeat_thread = threading.Thread(target=run_echo_loop, daemon=True)
            heartbeat_thread.start()

        self.logger.info("Echo loop started")

    def stop_echo_loop(self):
        """
        Stop the periodic echo loop.
        """
        if not self.echo_loop_running:
            return

        self.echo_loop_running = False

        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()

        self.logger.info("Echo loop stopped")

    async def check_employment_send(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Check if phone number is valid for employment send operation.

        Args:
            phone_number: Employee's phone number

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_id": null/1
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, using mock response for employment send check")
            # Mock responses for testing
            if phone_number == "123456789":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_id": 5  # Pre-reserved section
                }
            elif phone_number == "987654321":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_id": None  # No pre-reserved section
                }
            else:
                return {
                    "phone_number": phone_number,
                    "valid": False,
                    "section_id": None
                }

        payload = {
            "phone_number": phone_number
        }

        success, data = await self.send_to_server("/api/employment/send", payload, "employment_send")
        return data

    async def check_employment_deliver(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Check if phone number is valid for employment deliver operation.

        Args:
            phone_number: Employee's phone number

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_ids": null/[1,2,3]
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, using mock response for employment deliver check")
            # Mock responses for testing
            if phone_number == "123456789":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_ids": [1, 2, 3]  # Pre-reserved sections for delivery
                }
            elif phone_number == "555666777":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_ids": None  # No pre-reserved sections
                }
            else:
                return {
                    "phone_number": phone_number,
                    "valid": False,
                    "section_ids": None
                }

        payload = {
            "phone_number": phone_number
        }

        success, data = await self.send_to_server("/api/employment/deliver", payload, "employment_deliver")
        return data

    async def check_employment_reclaim(self, reclamation_pin: str) -> Optional[Dict[str, Any]]:
        """
        Check if reclamation PIN is valid for employment reclaim operation.

        Args:
            reclamation_pin: Reclamation PIN to validate

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_id": null/1
            }
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, using mock response for employment reclaim check")
            # Mock responses for testing
            if reclamation_pin == "RECLAIM123":
                return {
                    "phone_number": "123456789",
                    "valid": True,
                    "section_id": 7  # Pre-reserved section for reclaim
                }
            elif reclamation_pin == "RECLAIM456":
                return {
                    "phone_number": "987654321",
                    "valid": True,
                    "section_id": None  # No pre-reserved section
                }
            else:
                return {
                    "phone_number": None,
                    "valid": False,
                    "section_id": None
                }

        success, data = await self.send_to_server("/api/employment/reclaim", {"reservation_pin": reclamation_pin}, "employment_reclaim")
        return data
    

    async def get_layout(self, serial_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get layout data from external API or return mock data.
        If external API is disabled, returns mock response from JSON file.

        Args:
            serial_number: Serial number to use for the request. If None, uses self.serial_number

        Returns:
            Dict with layout data if successful, None otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, returning mock layout data")
            return self._get_mock_layout_response()

        payload = {
            "SerialNumber": serial_number or self.serial_number
        }

        success, data = await self.send_to_server("/api/layout", payload, "layout", method="get")
        return data

    def _get_mock_layout_response(self) -> Optional[Dict[str, Any]]:
        """
        Load and return mock layout response from JSON file.

        Returns:
            Dict with mock layout data if file exists and is valid, None otherwise
        """
        try:
            # Get the directory where this file is located
            current_dir = os.path.dirname(os.path.abspath(__file__))
            mock_file_path = os.path.join(current_dir, "mock_layout_response.json")

            if not os.path.exists(mock_file_path):
                self.logger.error(f"Mock layout response file not found: {mock_file_path}")
                return None

            with open(mock_file_path, 'r', encoding='utf-8') as f:
                mock_data = json.load(f)

            self.logger.info("Mock layout data loaded successfully")
            return mock_data

        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing mock layout JSON file: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading mock layout response: {e}")
            return None

    async def send_to_journal(
        self,
        serial_number: str,
        timestamp: str,
        entered_pin: Optional[str] = None,
        event_result: Optional[str] = None,
        event_type: Optional[str] = None,
        message: Optional[str] = None,
        operator_id: Optional[str] = None,
        order_number: Optional[str] = None,
        section_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tempered_unlock: Optional[str] = None,
        type: Optional[str] = None
    ) -> bool:
        """
        Send journal data to Jetveo API /api/journal endpoint.
        Only sends if external API is enabled. If request fails, saves to server_request_queue.

        Args:
            serial_number: Device serial number (required)
            timestamp: Event timestamp in ISO format (required)
            entered_pin: PIN that was entered
            event_result: Result of the event
            event_type: Type of event
            message: Event message
            operator_id: ID of the operator
            order_number: Order number
            section_id: Section ID
            session_id: Session ID
            tempered_unlock: Tampered unlock status
            type: Event type

        Returns:
            True if journal data sent successfully, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping journal data send")
            return True

        request_data = {
            "SerialNumber": serial_number,
            "Timestamp": timestamp,
            "EnteredPin": entered_pin or "",
            "EventResult": event_result or "",
            "EventType": event_type or "",
            "Message": message or "",
            "OperatorId": operator_id or "",
            "OrderNumber": order_number or "",
            "SectionId": section_id or "",
            "SessionId": session_id or "",
            "TemperedUnlock": tempered_unlock or "",
            "Type": type or ""
        }

        success, data = await self.send_to_server("/api/journal", request_data, "journal")
        return success


    def _save_failed_request(self, endpoint: str, action: str, payload: Dict[str, Any], method: str = "post"):
        """
        Save failed request to server_request_queue table.
        Saves important requests for retry, excludes heartbeat requests.
        Note: method column will be ignored if it doesn't exist in database.
        """
        # Save all requests except heartbeat (imalive) requests
        if endpoint == "/api/imalive":
            return

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Use the correct column name 'methond' as defined in the migration
            query = """
                INSERT INTO server_request_queue (
                    device_status, enpoint, action, error, payload, sent_attemps, methond
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            values = (
                1,  # device_status = 1 (active)
                endpoint,
                action,
                0,  # error = 0 (not successfully sent yet)
                json.dumps(payload),
                0,  # sent_attemps = 0 (initial)
                method
            )

            cursor.execute(query, values)
            conn.commit()
            self.logger.info(f"Failed request saved to queue: {endpoint}")

        except mysql.connector.Error as err:
            self.logger.error(f"Database error saving failed request: {err}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    async def server_ping(self) -> bool:
        """
        Check if server is available using /api/imalive endpoint.

        Returns:
            True if server is available and returns "success": true, False otherwise
        """
        if not self.external_api_enabled:
            return False

        try:
            url = f"{self.base_url}/api/imalive"        # later can be replaced by ping endpoint
            request_data = {
                "SerialNumber": self.serial_number
            }
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("success") is True
                    else:
                        return False

        except Exception as e:
            self.logger.debug(f"Server ping failed: {e}")
            return False


    async def retry_failed_requests(self) -> int:
        """
        Retry failed requests from server_request_queue.
        Only retries if server is available.

        Returns:
            Number of requests successfully retried
        """
        if not self.external_api_enabled:
            return 0
        
        self.logger.info("Retrying failed requests")

        # Check if server is available
        if not await self.server_ping():
            self.logger.debug("Server ping failed, skipping retry of failed requests")
            return 0

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Get failed requests that are ready to retry
            query = """
                SELECT id, enpoint, action, payload, sent_attemps, methond
                FROM server_request_queue
                WHERE error = 0 AND sent_attemps < 5
                ORDER BY created_at ASC
                LIMIT 10
            """
            cursor.execute(query)
            failed_requests = cursor.fetchall()

            successful_retries = 0

            for request_id, endpoint, action, payload_json, sent_attempts, method in failed_requests:
                try:
                    payload = json.loads(payload_json)

                    # Retry send failed request to server using send_to_server method
                    # Use stored method or default to "post" if None
                    retry_method = method if method else "post"
                    success, _ = await self.send_to_server(endpoint, payload, action, retry_method, called_from_retry=True)

                    if success:
                        # Mark as successfully sent (error = 1)
                        update_query = "UPDATE server_request_queue SET error = 1, sent_attemps = %s WHERE id = %s"
                        cursor.execute(update_query, (sent_attempts + 1, request_id))
                        successful_retries += 1
                        self.logger.info(f"Successfully retried request {request_id} to {endpoint}")
                    else:
                        # Increment sent_attempts
                        new_attempts = sent_attempts + 1
                        if new_attempts >= 5:
                            # Mark as unsuccessfully sent after 5 attempts (error = 2)
                            update_query = "UPDATE server_request_queue SET error = 2, sent_attemps = %s WHERE id = %s"
                            cursor.execute(update_query, (new_attempts, request_id))
                            self.logger.warning(f"Request {request_id} to {endpoint} marked as failed after 5 attempts")
                        else:
                            # Just increment attempts
                            update_query = "UPDATE server_request_queue SET sent_attemps = %s WHERE id = %s"
                            cursor.execute(update_query, (new_attempts, request_id))
                            self.logger.debug(f"Request {request_id} to {endpoint} retry failed, attempt {new_attempts}/5")

                except json.JSONDecodeError:
                    self.logger.error(f"Invalid JSON payload in request {request_id}")
                    # Mark as error (error = 2 for invalid data)
                    update_query = "UPDATE server_request_queue SET error = 2 WHERE id = %s"
                    cursor.execute(update_query, (request_id,))

            conn.commit()

            if successful_retries > 0:
                self.logger.info(f"Successfully retried {successful_retries} failed requests")

            return successful_retries

        except mysql.connector.Error as err:
            self.logger.error(f"Database error during retry: {err}")
            return 0
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()



    async def storage_change_status(
        self,
        reservation_uuid: str,
        serial_number: Optional[str] = None,
        reservation_pin: Optional[str] = None,
        section_id: Optional[int] = None,
        email: Optional[str] = None,
        size_category: Optional[int] = None,
        paid_price: Optional[float] = None,
        paid_fine: Optional[float] = None,
        timestamp: Optional[str] = None,
        action: Optional[int] = None,
        status: Optional[int] = None
    ) -> bool:
        """
        Send storage change status data to Jetveo API /api/product-change-status endpoint.
        Only sends if external API is enabled.

        Args:
            reservation_uuid: Reservation UUID (required)
            serial_number: Device serial number (defaults to env SERIAL_NUMBER)
            reservation_pin: Reservation PIN (optional)
            section_id: Section ID (optional)
            email: Customer email (optional)
            size_category: Size category (optional)
            paid_price: Paid price amount (optional)
            paid_fine: Paid fine amount (optional)
            timestamp: Timestamp in ISO format (optional)
            action: Action code (optional)
            status: Status code (optional)

        Returns:
            True if successful, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping storage change status")
            return False

        # Use default serial number if not provided
        if serial_number is None:
            serial_number = self.serial_number

        # Use current timestamp if not provided
        if timestamp is None:
            from datetime import datetime, timezone
            timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        request_data = {
            "SerialNumber": serial_number,
            "ReservationUuid": reservation_uuid,
            "ReservationPin": reservation_pin or "",
            "SectionId": section_id or 0,
            "Email": email or "",
            "SizeCategory": size_category or 0,
            "PaidPrice": paid_price or 0,
            "PaidFine": paid_fine or 0,
            "Timestamp": timestamp,
            "Action": action or 0,
            "Status": status or 0
        }

        success, data = await self.send_to_server("/api/product-change-status", request_data, "storage_change_status")
        return success

    async def product_change_status(
        self,
        reservation_uuid: str,
        serial_number: Optional[str] = None,
        reserved: Optional[bool] = None,
        reservation_pin: Optional[str] = None,
        section_id: Optional[int] = None,
        timestamp: Optional[str] = None,
        price: Optional[float] = None,
        ean: Optional[str] = None,
        action: Optional[int] = None,
        status: Optional[int] = None
    ) -> bool:
        """
        Send product change status data to Jetveo API /api/product-change-status endpoint.
        Only sends if external API is enabled.

        Args:
            reservation_uuid: Reservation UUID (required)
            serial_number: Device serial number (defaults to env SERIAL_NUMBER)
            reserved: Whether the product is reserved (optional)
            reservation_pin: Reservation PIN (optional)
            section_id: Section ID (optional)
            timestamp: Timestamp in ISO format (optional)
            price: Product price (optional)
            ean: Product EAN code (optional)
            action: Action code (optional)
            status: Status code (optional)

        Returns:
            True if successful, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.debug("External API is disabled, skipping product change status")
            return False

        # Use default serial number if not provided
        if serial_number is None:
            serial_number = self.serial_number

        # Use current timestamp if not provided
        if timestamp is None:
            from datetime import datetime, timezone
            timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        request_data = {
            "SerialNumber": serial_number,
            "ReservationUuid": reservation_uuid,
            "Reserved": reserved if reserved is not None else False,
            "ReservationPin": reservation_pin or "",
            "SectionId": section_id or 0,
            "Timestamp": timestamp,
            "Price": price or 0,
            "Ean": ean or "",
            "Action": action or 0,
            "Status": status or 0
        }

        success, data = await self.send_to_server("/api/product-change-status", request_data, "product_change_status")
        return success
        

    async def send_to_server(self, endpoint: str, payload: Dict[str, Any], action: str, method: str = "post", called_from_retry: bool = False) -> Tuple[bool, Dict[str, Any]]:
        """
        Function to send payload to entered endpoint on jetveo_server
        """
        if not self.external_api_enabled:
            return True, {}

        try:
            url = f"{self.base_url}{endpoint}"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.request(method, url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        self.logger.info(f"Request on server send succesfully: {response_data} for {endpoint}")
                        success = response_data.get("success", True)
                        data = response_data.get("data", {})
                        if not success and not called_from_retry:
                            self._save_failed_request(endpoint, action, payload, method)
                        return success, data
                    else:
                        self.logger.error(f"Request on server failed with status {response.status} for {endpoint}")
                        if endpoint not in ["/api/imalive"] and not called_from_retry:
                            self._save_failed_request(endpoint, action, payload, method)
                        return False, None

        except asyncio.TimeoutError:
            self.logger.error(f"Request on server failed with timeout for {endpoint}")
            if not called_from_retry:
                self._save_failed_request(endpoint, action, payload, method)
            return False, None
        except Exception as e:
            self.logger.error(f"Error sending request to server: {e} for {endpoint}")
            if not called_from_retry:
                self._save_failed_request(endpoint, action, payload, method)
            return False, None



    async def send_temperature_electronic(self, sensors: List[Dict[str, Any]]) -> bool:
        """
        Send electronic temperature sensor data to Jetveo API.

        Args:
            sensors: List of sensor dictionaries in format [{"id": "1", "value": 23.5}, {"id": "2", "value": 24.1}]

        Returns:
            True if successful, False otherwise

        Expected payload format:
        {
            "serial_number": "123456-789",
            "timestamp": "1971-01-28T13:39:08.015Z",
            "sensors": [
                {"id": "1", "value": 5},
                {"id": "2", "value": 6}
            ]
        }
        """
        from datetime import datetime, timezone

        # Sensors array is already in the correct format, just ensure proper types
        sensors_array = []
        for sensor_item in sensors:
            sensors_array.append({
                "id": str(sensor_item["id"]),
                "value": sensor_item["value"]
            })

        # Prepare payload
        payload = {
            "serial_number": self.serial_number,
            "timestamp": datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z'),
            "sensors": sensors_array
        }

        success, _ = await self.send_to_server("/api/post-temperature-electronic", payload, "temperature_electronic")
        return success


    async def send_temperature_comet(self, sensors: List[Dict[str, Any]]) -> bool:
        """
        Send Comet temperature sensor data to Jetveo API.

        Args:
            sensors: List of sensor dictionaries in format [{"id": "*************", "value": 21.3}, {"id": "*************", "value": 22.1}]

        Returns:
            True if successful, False otherwise

        Expected payload format:
        {
            "serial_number": "123456-789",
            "timestamp": "1971-01-28T13:39:08.015Z",
            "sensors": [
                {"id": "*************", "value": 21.3},
                {"id": "*************", "value": 22.1}
            ]
        }
        """
        from datetime import datetime, timezone

        # Sensors array is already in the correct format, just ensure proper types
        sensors_array = []
        for sensor_item in sensors:
            sensors_array.append({
                "id": str(sensor_item["id"]),
                "value": sensor_item["value"]
            })

        # Prepare payload
        payload = {
            "serial_number": self.serial_number,
            "timestamp": datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z'),
            "sensors": sensors_array
        }

        success, _ = await self.send_to_server("/api/post-temperature-comet", payload, "temperature_comet")
        return success



    def check_order_number_deliver(self, order_number: str) -> bool:
        """
        Check if order number is valid for customer deliver operation.

        Args:
            order_number: Order number

        Returns:
            True if valid, False otherwise
        """
        # TODO: Implement check with external API, but later, this endpoint is not implemented on Jetveo side yet
        return True


    

# Global client instance
jetveo_client = JetveoClient()

def storage_change_status_async(
    reservation_uuid: str,
    serial_number: Optional[str] = None,
    reservation_pin: Optional[str] = None,
    section_id: Optional[int] = None,
    email: Optional[str] = None,
    size_category: Optional[int] = None,
    paid_price: Optional[float] = None,
    paid_fine: Optional[float] = None,
    timestamp: Optional[str] = None,
    action: Optional[int] = None,
    status: Optional[int] = None
):
    """
    Synchronous wrapper for storage_change_status that runs the async call in background.
    Similar to how log_timeline_event() works - call it and it handles the async part.

    Args:
        reservation_uuid: Reservation UUID (required)
        serial_number: Device serial number (defaults to env SERIAL_NUMBER)
        reservation_pin: Reservation PIN (optional)
        section_id: Section ID (optional)
        email: Customer email (optional)
        size_category: Size category (optional)
        paid_price: Paid price amount (optional)
        paid_fine: Paid fine amount (optional)
        timestamp: Timestamp in ISO format (optional, defaults to current time)
        action: Action code (optional)
        status: Status code (optional)
    """
    from os import getenv

    # Check if external API is enabled
    external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
    if not external_api_enabled:
        return

    import threading
    import asyncio

    def run_async_call():
        """Run the async call in a separate thread with its own event loop"""
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the async call
            loop.run_until_complete(jetveo_client.storage_change_status(
                reservation_uuid=reservation_uuid,
                serial_number=serial_number,
                reservation_pin=reservation_pin,
                section_id=section_id,
                email=email,
                size_category=size_category,
                paid_price=paid_price,
                paid_fine=paid_fine,
                timestamp=timestamp,
                action=action,
                status=status
            ))

            loop.close()

        except Exception as e:
            # Log error but don't fail the calling operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in storage_change_status_async: {e}")

    # Start the async call in a separate daemon thread so it doesn't block
    thread = threading.Thread(target=run_async_call, daemon=True)
    thread.start()

def product_change_status_async(
    reservation_uuid: str,
    serial_number: Optional[str] = None,
    reserved: Optional[bool] = None,
    reservation_pin: Optional[str] = None,
    section_id: Optional[int] = None,
    timestamp: Optional[str] = None,
    price: Optional[float] = None,
    ean: Optional[str] = None,
    action: Optional[int] = None,
    status: Optional[int] = None
):
    """
    Synchronous wrapper for product_change_status that runs the async call in background.
    Similar to how log_timeline_event() works - call it and it handles the async part.

    Args:
        reservation_uuid: Reservation UUID (required)
        serial_number: Device serial number (defaults to env SERIAL_NUMBER)
        reserved: Whether the product is reserved (optional)
        reservation_pin: Reservation PIN (optional)
        section_id: Section ID (optional)
        timestamp: Timestamp in ISO format (optional, defaults to current time)
        price: Product price (optional)
        ean: Product EAN code (optional)
        action: Action code (optional)
        status: Status code (optional)
    """
    from os import getenv

    # Check if external API is enabled
    external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
    if not external_api_enabled:
        return

    import threading
    import asyncio

    def run_async_call():
        """Run the async call in a separate thread with its own event loop"""
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the async call
            loop.run_until_complete(jetveo_client.product_change_status(
                reservation_uuid=reservation_uuid,
                serial_number=serial_number,
                reserved=reserved,
                reservation_pin=reservation_pin,
                section_id=section_id,
                timestamp=timestamp,
                price=price,
                ean=ean,
                action=action,
                status=status
            ))

            loop.close()

        except Exception as e:
            # Log error but don't fail the calling operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in product_change_status_async: {e}")

    # Start the async call in a separate daemon thread so it doesn't block
    thread = threading.Thread(target=run_async_call, daemon=True)
    thread.start()

def start_jetveo_echo_loop():
    """
    Start the Jetveo echo loop service.
    Call this function during application startup.
    """
    jetveo_client.start_echo_loop()

def stop_jetveo_echo_loop():
    """
    Stop the Jetveo echo loop service.
    Call this function during application shutdown.
    """
    jetveo_client.stop_echo_loop()


